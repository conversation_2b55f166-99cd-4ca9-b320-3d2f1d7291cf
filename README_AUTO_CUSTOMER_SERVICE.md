# 飞书自动客服检测功能

## 功能说明

修改后的 `FlyBookAutoAsync.py` 脚本新增了自动检测客服接通消息的功能。当检测到特定的客服接通消息模式时，会自动运行 `simple_send_message.py` 脚本并将当前聊天ID传递给它。

## 检测模式

脚本会检测包含以下模式的消息：
```
已接通人工.*?@.*?为你服务.*?请问.*?帮你
```

### 匹配示例

以下消息会被检测到：
- "已接通人工@客服小王为你服务，请问有什么可以帮你的吗？"
- "已接通人工@张三为你服务，请问需要什么帮你？"
- "已接通人工@客服小美为你服务，请问我能帮你做什么？"

### 不匹配示例

以下消息不会被检测到：
- "普通消息，没有客服接通内容"
- "已接通人工，但是没有@符号"
- "@客服为你服务，但是没有已接通人工"

## 工作流程

1. **消息监听**: `FlyBookAutoAsync.py` 持续监听飞书消息
2. **模式检测**: 检测到客服接通消息时触发处理
3. **ID记录**: 记录当前聊天的 `chat_id`
4. **脚本更新**: 自动更新 `simple_send_message.py` 中的 `chat_id`
5. **脚本执行**: 运行 `simple_send_message.py` 发送消息
6. **重复处理防护**: 同一聊天ID只处理一次，避免重复执行

## 文件修改

### FlyBookAutoAsync.py 新增功能

1. **新增属性**:
   ```python
   self.processed_chats = set()  # 记录已处理的聊天ID
   self.script_path = r"D:\个人开发\OpenFeiShuApis-master3.0\simple_send_message.py"
   ```

2. **新增方法**:
   - `check_customer_service_pattern(text)`: 检测客服消息模式
   - `update_script_chat_id(chat_id)`: 更新脚本中的chat_id
   - `run_send_message_script(chat_id)`: 异步运行发送消息脚本

3. **消息处理逻辑**:
   - 在原有消息处理逻辑前添加客服消息检测
   - 检测到客服消息时自动处理
   - 保留原有的消息转发功能

### simple_send_message.py 要求

脚本中必须包含以下格式的chat_id定义：
```python
chat_id = "7436440564930822146"  # 请替换为实际的聊天ID
```

## 使用方法

1. **确保文件路径正确**:
   - 确认 `simple_send_message.py` 位于指定路径
   - 如需修改路径，请更新 `FlyBookAutoAsync.py` 中的 `self.script_path`

2. **运行监听脚本**:
   ```bash
   python FlyBookAutoAsync.py
   ```

3. **等待触发**:
   - 脚本会持续监听消息
   - 当检测到客服接通消息时自动处理

## 日志输出

当检测到客服消息时，会输出以下日志：
```
检测到客服接通消息!
消息内容: 已接通人工@客服小王为你服务，请问有什么可以帮你的吗？
开始处理聊天ID: 1234567890123456789
已更新脚本中的chat_id为: 1234567890123456789
正在运行发送消息脚本，目标聊天ID: 1234567890123456789
脚本执行成功
成功处理聊天ID: 1234567890123456789
```

## 错误处理

1. **脚本文件不存在**: 会输出错误信息但不影响主程序运行
2. **脚本执行失败**: 会输出错误详情和返回码
3. **chat_id更新失败**: 会跳过脚本执行
4. **重复处理**: 同一聊天ID只处理一次

## 测试

运行测试脚本验证功能：
```bash
python test_pattern_detection.py
```

测试包括：
- 客服消息模式检测测试
- chat_id替换功能测试

## 注意事项

1. **路径配置**: 确保脚本路径正确，使用绝对路径避免问题
2. **权限问题**: 确保有权限读写 `simple_send_message.py` 文件
3. **并发处理**: 使用异步处理避免阻塞主消息监听循环
4. **重复防护**: 通过 `processed_chats` 集合避免重复处理
5. **原有功能**: 保留了原有的消息转发功能，不影响现有使用

## 自定义配置

### 修改检测模式

如需修改检测的消息模式，请编辑 `check_customer_service_pattern` 方法中的正则表达式：
```python
def check_customer_service_pattern(self, text):
    pattern = r'你的自定义模式'
    return re.search(pattern, text, re.DOTALL) is not None
```

### 修改脚本路径

如需修改要执行的脚本路径，请更新：
```python
self.script_path = r"你的脚本路径"
```

### 修改chat_id替换模式

如果 `simple_send_message.py` 中的chat_id格式不同，请修改 `update_script_chat_id` 方法中的正则表达式。
