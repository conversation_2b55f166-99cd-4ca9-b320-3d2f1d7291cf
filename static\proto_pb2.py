# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bproto.proto\"\xa6\x01\n\x05\x46rame\x12\r\n\x05seqid\x18\x01 \x01(\x04\x12\r\n\x05logid\x18\x02 \x01(\x04\x12\x0f\n\x07service\x18\x03 \x01(\x05\x12\x0e\n\x06method\x18\x04 \x01(\x05\x12\x1f\n\x07headers\x18\x05 \x03(\x0b\x32\x0e.ExtendedEntry\x12\x17\n\x0fpayloadEncoding\x18\x06 \x01(\t\x12\x13\n\x0bpayloadType\x18\x07 \x01(\t\x12\x0f\n\x07payload\x18\x08 \x01(\x0c\"\x9d\x02\n\x06Packet\x12\x0b\n\x03sid\x18\x01 \x01(\t\x12!\n\x0bpayloadType\x18\x02 \x01(\x0e\x32\x0c.PayloadType\x12\x0b\n\x03\x63md\x18\x03 \x01(\x05\x12\x0e\n\x06status\x18\x04 \x01(\r\x12\x0f\n\x07payload\x18\x05 \x01(\x0c\x12\x0b\n\x03\x63id\x18\x06 \x01(\t\x12\x1f\n\npipeEntity\x18\x07 \x01(\x0b\x32\x0b.PipeEntity\x12(\n\x0fversionPayloads\x18\x08 \x03(\x0b\x32\x0f.VersionPayload\x12!\n\x0cpipeEntities\x18\t \x03(\x0b\x32\x0b.PipeEntity\x12\x19\n\x11waitRetryInterval\x18\n \x01(\r\x12\x0f\n\x07\x63ommand\x18\x0b \x01(\x05\x12\x0e\n\x06\x63ursor\x18\x0c \x01(\x04\"\x8f\x01\n\x13PushMessagesRequest\x12\x34\n\x08messages\x18\x01 \x03(\x0b\x32\".PushMessagesRequest.MessagesEntry\x1a\x42\n\rMessagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12 \n\x05value\x18\x02 \x01(\x0b\x32\x11.entities.Message:\x02\x38\x01\"\xa3\x02\n\x11PutMessageRequest\x12\x13\n\x04type\x18\x01 \x01(\x0e\x32\x05.Type\x12\x19\n\x07\x63ontent\x18\x02 \x01(\x0b\x32\x08.Content\x12\x0e\n\x06\x63hatId\x18\x03 \x01(\t\x12\x0e\n\x06rootId\x18\x04 \x01(\t\x12\x10\n\x08parentId\x18\x05 \x01(\t\x12\x0b\n\x03\x63id\x18\x06 \x01(\t\x12\x12\n\nisNotified\x18\x07 \x01(\x08\x12\x12\n\nsendToChat\x18\x08 \x01(\x08\x12\x0f\n\x07version\x18\t \x01(\x05\x12\x1a\n\x12isThreadGroupTopic\x18\n \x01(\x08\x12\x13\n\x0bisAnonymous\x18\x0b \x01(\x08\x12\x1e\n\x16leftStaticResourceKeys\x18\x65 \x03(\t\x12\x15\n\rthriftMessage\x18\x66 \x01(\x0c\"\xc6\x05\n\x0ePutChatRequest\x12\x13\n\x04type\x18\x01 \x01(\x0e\x32\x05.Type\x12\x0f\n\x07userIds\x18\x02 \x03(\t\x12\x11\n\tgroupName\x18\x03 \x01(\t\x12\x11\n\tgroupDesc\x18\x04 \x01(\t\x12\x10\n\x08isPublic\x18\x05 \x01(\x08\x12\x12\n\nchatterIds\x18\x06 \x03(\t\x12\x16\n\x0eorganizationId\x18\x07 \x01(\t\x12\x12\n\nfromChatId\x18\x08 \x01(\t\x12\x16\n\x0einitMessageIds\x18\t \x03(\t\x12\x0f\n\x07iconKey\x18\n \x01(\t\x12/\n\x08\x64ocPerms\x18\x0b \x03(\x0b\x32\x1d.PutChatRequest.DocPermsEntry\x12\x15\n\risCrossTenant\x18\x0c \x01(\x08\x12\x12\n\nisPublicV2\x18\r \x01(\x08\x12\x31\n\tdocPerms2\x18\x0f \x03(\x0b\x32\x1e.PutChatRequest.DocPerms2Entry\x12\x0b\n\x03\x63id\x18\x10 \x01(\t\x1aL\n\rDocPermsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0e\x32\x1b.PutChatRequest.DocPermType:\x02\x38\x01\x1aI\n\x0e\x44ocPerms2Entry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.PutChatRequest.DocPair:\x02\x38\x01\x1a\x87\x01\n\x07\x44ocPair\x12\x31\n\x05perms\x18\x01 \x03(\x0b\x32\".PutChatRequest.DocPair.PermsEntry\x1aI\n\nPermsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0e\x32\x1b.PutChatRequest.DocPermType:\x02\x38\x01\".\n\x0b\x44ocPermType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04READ\x10\x01\x12\x08\n\x04\x45\x44IT\x10\x02\"C\n\x0fPutChatResponse\x12\x13\n\x04\x63hat\x18\x01 \x01(\x0b\x32\x05.Chat\x12\x1b\n\x08\x66\x65\x65\x64\x43\x61rd\x18\x02 \x01(\x0b\x32\t.FeedCard\"D\n\x16UniversalSearchRequest\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.SearchCommonRequestHeader\"\xff\x11\n\x17UniversalSearchResponse\x12\x43\n\x06header\x18\x01 \x01(\x0b\x32\x33.UniversalSearchResponse.SearchCommonResponseHeader\x12\x36\n\x07results\x18\x02 \x03(\x0b\x32%.UniversalSearchResponse.SearchResult\x12?\n\x0b\x65xtraFields\x18\x03 \x01(\x0b\x32*.UniversalSearchResponse.SearchExtraFields\x12\x44\n\x11\x66\x61iledEntityInfos\x18\x05 \x03(\x0b\x32).UniversalSearchResponse.FailedEntityInfo\x1a\xd3\x01\n\x0cSearchResult\x12\n\n\x02id\x18\x01 \x01(\t\x12\x37\n\x04type\x18\x02 \x01(\x0e\x32).UniversalSearchResponse.SearchEntityType\x12\x18\n\x10titleHighlighted\x18\x03 \x01(\t\x12\x1a\n\x12summaryHighlighted\x18\x04 \x01(\t\x12\x19\n\x11\x65xtrasHighlighted\x18\x05 \x01(\t\x12\x11\n\tavatarKey\x18\x06 \x01(\t\x12\x1a\n\x12\x65xtraInfoSeparator\x18\n \x01(\t\x1a\x93\x07\n\x1aSearchCommonResponseHeader\x12\x15\n\rsearchSession\x18\x01 \x01(\t\x12\x14\n\x0csessionSeqId\x18\x02 \x01(\x05\x12\r\n\x05total\x18\x03 \x01(\x05\x12\x0f\n\x07hasMore\x18\x04 \x01(\x08\x12\x17\n\x0fpaginationToken\x18\x05 \x01(\t\x12\x66\n\x14invokeAbnormalNotice\x18\x06 \x01(\x0e\x32H.UniversalSearchResponse.SearchCommonResponseHeader.InvokeAbnormalNotice\x12^\n\x0bstorageInfo\x18\x07 \x01(\x0b\x32I.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfo\x1a\xd6\x03\n\x15\x43oldAndHotStorageInfo\x12\x16\n\x0eisNeedColdData\x18\x01 \x01(\x08\x12\x10\n\x08timeSize\x18\x02 \x01(\x05\x12\x64\n\x08timeUnit\x18\x03 \x01(\x0e\x32R.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfo.TimeUnit\x12\x10\n\x08timeText\x18\x04 \x01(\t\x12j\n\x0bhasMoreInfo\x18\x05 \x01(\x0e\x32U.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfo.HasMoreInfo\"C\n\x08TimeUnit\x12\x0f\n\x0bTimeUNKNOWN\x10\x00\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x08\n\x04WEEK\x10\x02\x12\t\n\x05MONTH\x10\x03\x12\x08\n\x04YEAR\x10\x04\"j\n\x0bHasMoreInfo\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x10\n\x0cHOT_HAS_MORE\x10\x01\x12\x13\n\x0fHOT_HAS_NO_MORE\x10\x02\x12\x11\n\rCOLD_HAS_MORE\x10\x03\x12\x14\n\x10\x43OLD_HAS_NO_MORE\x10\x04\"n\n\x14InvokeAbnormalNotice\x12\x11\n\rINVOKE_NORMAL\x10\x00\x12\x14\n\x10REQUEST_CANCELED\x10\x01\x12\x12\n\x0eVERSION_SWITCH\x10\x02\x12\x19\n\x15QUERY_LENGTH_EXCEEDED\x10\x03\x1a\x36\n\x11SearchExtraFields\x12!\n\x19\x63hatterPermissionResponse\x18\x01 \x01(\x0c\x1a\xec\x03\n\x10\x46\x61iledEntityInfo\x12N\n\nentityType\x18\x01 \x01(\x0e\x32:.UniversalSearchResponse.FailedEntityInfo.SearchEntityType\x12\x1b\n\x13isNeedLocalFallback\x18\x02 \x01(\x08\x12\x1c\n\x14localPaginationToken\x18\x03 \x01(\t\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\"\xdb\x03\n\x19SearchCommonRequestHeader\x12\x15\n\rsearchSession\x18\x01 \x01(\t\x12\x14\n\x0csessionSeqId\x18\x02 \x01(\x05\x12\r\n\x05query\x18\x03 \x01(\t\x12\x17\n\x0fpaginationToken\x18\x04 \x01(\t\x12\x30\n\rsearchContext\x18\x05 \x01(\x0b\x32\x19.BaseEntity.SearchContext\x12\x0e\n\x06locale\x18\x06 \x01(\t\x12\x14\n\x0cimpressionId\x18\x07 \x01(\t\x12%\n\nextraParam\x18\x08 \x01(\x0b\x32\x11.SearchExtraParam\x12\x36\n\x0btitleLayout\x18\t \x01(\x0b\x32!.SearchCommonRequestHeader.Layout\x12\x38\n\rsummaryLayout\x18\n \x01(\x0b\x32!.SearchCommonRequestHeader.Layout\x12\x10\n\x08pageSize\x18\x0b \x01(\x05\x12?\n\x14sectionSummaryLayout\x18\r \x01(\x0b\x32!.SearchCommonRequestHeader.Layout\x1a%\n\x06Layout\x12\x0c\n\x04line\x18\x01 \x01(\x05\x12\r\n\x05width\x18\x02 \x01(\x05\"M\n\x10SearchExtraParam\x12 \n\x18\x63hatterPermissionRequest\x18\x01 \x01(\x0c\x12\x17\n\x0fqueryInputState\x18\x02 \x01(\x05\"\x88\x01\n\nBaseEntity\x1az\n\rSearchContext\x12\x0f\n\x07tagName\x18\x01 \x01(\t\x12 \n\x0b\x65ntityItems\x18\x02 \x03(\x0b\x32\x0b.EntityItem\x12#\n\x0c\x63ommonFilter\x18\x03 \x01(\x0b\x32\r.CommonFilter\x12\x11\n\tsourceKey\x18\x05 \x01(\t\":\n\x0c\x43ommonFilter\x12\x1a\n\x12includeOuterTenant\x18\x01 \x01(\x08\x12\x0e\n\x06\x63hatId\x18\x02 \x01(\t\"\xe8\x01\n\nUserFilter\x12\x12\n\nisResigned\x18\x01 \x01(\x08\x12\x13\n\x0bhaveChatter\x18\x02 \x01(\x08\x12\x33\n\x0c\x63ustomFields\x18\x03 \x03(\x0b\x32\x1d.UserFilter.CustomFieldsEntry\x12\x0f\n\x07\x65xclude\x18\x04 \x01(\x08\x1a\x1d\n\x0b\x46ieldValues\x12\x0e\n\x06values\x18\x01 \x03(\t\x1aL\n\x11\x43ustomFieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.UserFilter.FieldValues:\x02\x38\x01\"\x90\x01\n\x0fGroupChatFilter\x12 \n\x0bsearchTypes\x18\x01 \x03(\x0e\x32\x0b.SearchType\x12\x15\n\rchatMemberIds\x18\x02 \x03(\t\x12\x17\n\x0f\x65xcludedChatIds\x18\x03 \x03(\t\x12\x14\n\x0csearchCrypto\x18\x04 \x01(\x08\x12\x15\n\raddableAsUser\x18\x05 \x01(\x08\"\xcf\x04\n\nEntityItem\x12*\n\x04type\x18\x01 \x01(\x0e\x32\x1c.EntityItem.SearchEntityType\x12(\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x18.EntityItem.EntityFilter\x12\x14\n\x0c\x62oostChatIds\x18\x04 \x03(\t\x12\x1c\n\x14localPaginationToken\x18\x05 \x01(\t\x1ah\n\x0c\x45ntityFilter\x12!\n\nuserFilter\x18\x01 \x01(\x0b\x32\x0b.UserFilterH\x00\x12+\n\x0fgroupChatFilter\x18\x02 \x01(\x0b\x32\x10.GroupChatFilterH\x00\x42\x08\n\x06\x66ilter\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\"\xa4\x04\n\x07\x43ontent\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x10\n\x08imageKey\x18\x02 \x01(\t\x12\x16\n\x0eisOriginSource\x18\x1f \x01(\x08\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x12\n\nisNotified\x18\x05 \x01(\x08\x12\x10\n\x08\x61udioKey\x18\x07 \x01(\t\x12\x15\n\raudioDuration\x18\x08 \x01(\x05\x12\x0e\n\x06\x63hatId\x18\t \x01(\t\x12\x13\n\x0b\x63ryptoToken\x18\n \x01(\t\x12\x0f\n\x07\x66ileKey\x18\x06 \x01(\t\x12\x10\n\x08\x66ileName\x18\x0b \x01(\t\x12\x10\n\x08\x66ileMime\x18\x0c \x01(\t\x12\x10\n\x08\x66ileSize\x18\r \x01(\x03\x12-\n\rfileTransMode\x18\x1c \x01(\x0e\x32\x16.Content.FileTransMode\x12\x16\n\x0esenderDeviceId\x18\x1d \x01(\t\x12\x1b\n\x08richText\x18\x0e \x01(\x0b\x32\t.RichText\x12\x10\n\x08\x64uration\x18\x0f \x01(\x05\x12\x16\n\x0e\x61ttendeesCount\x18\x11 \x01(\x05\x12\x1b\n\x13isGroupAnnouncement\x18\x12 \x01(\x08\x12\x14\n\x0cstickerSetId\x18\x18 \x01(\t\x12\x11\n\tstickerId\x18\x19 \x01(\t\x12\x13\n\x0bshareUserId\x18\x1b \x01(\t\"+\n\rFileTransMode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\r\n\tLAN_TRANS\x10\x01\"\xe2\x0e\n\x08\x65ntities\x1a\xf5\x0b\n\x07Message\x12\n\n\x02id\x18\x01 \x01(\t\x12$\n\x04type\x18\x02 \x01(\x0e\x32\x16.entities.Message.Type\x12\x0e\n\x06\x66romId\x18\x03 \x01(\t\x12\x12\n\ncreateTime\x18\x04 \x01(\x03\x12\x0f\n\x07\x63ontent\x18\x05 \x01(\x0c\x12 \n\x06status\x18\x06 \x01(\x0e\x32\x10.entities.Status\x12$\n\x08\x66romType\x18\x07 \x01(\x0e\x32\x12.entities.FromType\x12\x0e\n\x06rootId\x18\x08 \x01(\t\x12\x10\n\x08parentId\x18\t \x01(\t\x12\x0e\n\x06\x63hatId\x18\n \x01(\t\x12\x16\n\x0elastModifyTime\x18\x0b \x01(\x03\x12\x0b\n\x03\x63id\x18\x0c \x01(\t\x12\x10\n\x08position\x18\r \x01(\x05\x12\x12\n\nupdateTime\x18\x0e \x01(\x03\x12\x12\n\nisNotified\x18\x0f \x01(\x08\x12\x12\n\nreplyCount\x18\x10 \x01(\t\x12\x1d\n\x15parentSourceMessageId\x18\x11 \x01(\t\x12\x1b\n\x13rootSourceMessageId\x18\x12 \x01(\t\x12\x0e\n\x06isDing\x18\x13 \x01(\x08\x12\x10\n\x08threadId\x18\x14 \x01(\t\x12\x12\n\nsendToChat\x18\x15 \x01(\x08\x12\x13\n\x0bisTruncated\x18\x16 \x01(\x08\x12\x11\n\tisRemoved\x18\x17 \x01(\x08\x12\x11\n\tchannelId\x18\x18 \x01(\t\x12\x16\n\x0ethreadPosition\x18\x1c \x01(\x05\x12\x11\n\tremoverId\x18\x1d \x01(\x03\x12\x19\n\x11translateLanguage\x18\x1e \x01(\t\x12*\n\x0bremoverType\x18\x1f \x01(\x0e\x32\x15.entities.RemoverType\x12\x15\n\rnoBadgedCount\x18! \x01(\x05\x12\x10\n\x08isBadged\x18\" \x01(\x08\x12\x12\n\nbadgeCount\x18# \x01(\x05\x12\x18\n\x10threadBadgeCount\x18$ \x01(\x05\x12\x18\n\x10threadReplyCount\x18% \x01(\x05\x12\x17\n\x0f\x61tOutChatterIds\x18& \x03(\t\x12\x17\n\x0fmessageLanguage\x18\' \x01(\t\x12\x18\n\x10isNoTraceRemoved\x18) \x01(\x08\x12\"\n\x1aisAutoTranslatedByReceiver\x18* \x01(\x08\x12\x31\n\x0bsensitivity\x18+ \x01(\x0e\x32\x1c.entities.MessageSensitivity\x12\x13\n\x0bisVisibleV2\x18, \x01(\x08\x12$\n\x08\x63hatType\x18. \x01(\x0e\x32\x12.entities.ChatType\x12\x18\n\x10originalSenderId\x18/ \x01(\t\x12&\n\x1eisStaticResourceMessageDeleted\x18\x30 \x01(\x08\x12\x1a\n\x12messagePipeVersion\x18\x34 \x01(\x03\x12\x1b\n\x13isBatchCopyMessages\x18\x35 \x01(\x08\x12\x16\n\x0eisSpecialFocus\x18\x38 \x01(\x08\x12\x17\n\x0fisIncludeDocUrl\x18: \x01(\x08\x12\x10\n\x08\x63ipherId\x18; \x01(\x03\"\xe9\x02\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04POST\x10\x02\x12\x08\n\x04\x46ILE\x10\x03\x12\x08\n\x04TEXT\x10\x04\x12\t\n\x05IMAGE\x10\x05\x12\n\n\x06SYSTEM\x10\x06\x12\t\n\x05\x41UDIO\x10\x07\x12\t\n\x05\x45MAIL\x10\x08\x12\x14\n\x10SHARE_GROUP_CHAT\x10\t\x12\x0b\n\x07STICKER\x10\n\x12\x11\n\rMERGE_FORWARD\x10\x0b\x12\x0c\n\x08\x43\x41LENDAR\x10\x0c\x12\x0e\n\nCLOUD_FILE\x10\r\x12\x08\n\x04\x43\x41RD\x10\x0e\x12\t\n\x05MEDIA\x10\x0f\x12\x18\n\x14SHARE_CALENDAR_EVENT\x10\x10\x12\x0b\n\x07HONGBAO\x10\x11\x12\x14\n\x10GENERAL_CALENDAR\x10\x12\x12\x0e\n\nVIDEO_CHAT\x10\x13\x12\x0c\n\x08LOCATION\x10\x14\x12\x1a\n\x16\x43OMMERCIALIZED_HONGBAO\x10\x16\x12\x13\n\x0fSHARE_USER_CARD\x10\x17\x12\x08\n\x04TODO\x10\x18\x12\n\n\x06\x46OLDER\x10\x19\"3\n\x08\x46romType\x12\x14\n\x10UNKNOWN_FROMTYPE\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\"C\n\x06Status\x12\x12\n\x0eUNKNOWN_STATUS\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\x0b\n\x07\x44\x45LETED\x10\x02\x12\x0c\n\x08MODIFIED\x10\x03\"T\n\x0bRemoverType\x12\x17\n\x13UNKNOWN_REMOVERTYPE\x10\x00\x12\x0e\n\nGROUPOWNER\x10\x01\x12\x0c\n\x08SYSADMIN\x10\x02\x12\x0e\n\nGROUPADMIN\x10\x03\"F\n\x12MessageSensitivity\x12\x17\n\x13UNKNOWN_SENSITIVITY\x10\x00\x12\x08\n\x04SAFE\x10\x01\x12\r\n\tDANGEROUS\x10\x02\"F\n\x08\x43hatType\x12\x15\n\x11UNKNOWN_CHAT_TYPE\x10\x00\x12\x07\n\x03P2P\x10\x01\x12\t\n\x05GROUP\x10\x02\x12\x0f\n\x0bTOPIC_GROUP\x10\x03\"8\n\x0bTextContent\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x1b\n\x08richText\x18\x03 \x01(\x0b\x32\t.RichText\"\xfb\x01\n\x08RichText\x12\x12\n\nelementIds\x18\x01 \x03(\t\x12\x11\n\tinnerText\x18\x02 \x01(\t\x12#\n\x08\x65lements\x18\x03 \x01(\x0b\x32\x11.RichTextElements\x12\x10\n\x08imageIds\x18\x05 \x03(\t\x12\r\n\x05\x61tIds\x18\x06 \x03(\t\x12\x11\n\tanchorIds\x18\x07 \x03(\t\x12\x0f\n\x07i18nIds\x18\x08 \x03(\t\x12\x10\n\x08mediaIds\x18\t \x03(\t\x12\x0f\n\x07\x64ocsIds\x18\n \x03(\t\x12\x16\n\x0einteractiveIds\x18\x0b \x03(\t\x12\x12\n\nmentionIds\x18\x0c \x03(\t\x12\x0f\n\x07version\x18\r \x01(\x05\"\xb2\x03\n\x10RichTextElements\x12\x35\n\ndictionary\x18\x01 \x03(\x0b\x32!.RichTextElements.DictionaryEntry\x12\x33\n\tstyleRefs\x18\x02 \x03(\x0b\x32 .RichTextElements.StyleRefsEntry\x12/\n\x06styles\x18\x03 \x03(\x0b\x32\x1f.RichTextElements.RichTextStyle\x1a\x43\n\x0f\x44ictionaryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1f\n\x05value\x18\x02 \x01(\x0b\x32\x10.RichTextElement:\x02\x38\x01\x1a\\\n\x0eStyleRefsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.RichTextElements.RichTextElementStyleRefs:\x02\x38\x01\x1a,\n\rRichTextStyle\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x1a\x30\n\x18RichTextElementStyleRefs\x12\x14\n\x08styleIds\x18\x01 \x03(\x05\x42\x02\x10\x00\"\xfc\x04\n\x0fRichTextElement\x12!\n\x03tag\x18\x01 \x01(\x0e\x32\x14.RichTextElement.Tag\x12*\n\x05style\x18\x02 \x03(\x0b\x32\x1b.RichTextElement.StyleEntry\x12\x10\n\x08property\x18\x03 \x01(\x0c\x12\x10\n\x08\x63hildIds\x18\x04 \x03(\t\x12\x11\n\tstyleKeys\x18\x05 \x03(\t\x1a,\n\nStyleEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xb4\x03\n\x03Tag\x12\x0f\n\x0bUNKNOWN_TAG\x10\x00\x12\x08\n\x04TEXT\x10\x01\x12\x07\n\x03IMG\x10\x02\x12\x05\n\x01P\x10\x03\x12\n\n\x06\x46IGURE\x10\x04\x12\x06\n\x02\x41T\x10\x05\x12\x05\n\x01\x41\x10\x06\x12\x05\n\x01\x42\x10\x07\x12\x05\n\x01I\x10\x08\x12\x05\n\x01U\x10\t\x12\x0b\n\x07\x45MOTION\x10\n\x12\n\n\x06\x42UTTON\x10\x0b\x12\n\n\x06SELECT\x10\x0c\x12\x1a\n\x16PROGRESS_SELECT_OPTION\x10\r\x12\x07\n\x03\x44IV\x10\x0e\x12\x11\n\rTEXTABLE_AREA\x10\x0f\x12\x08\n\x04TIME\x10\x10\x12\x08\n\x04LINK\x10\x11\x12\t\n\x05MEDIA\x10\x12\x12\x0e\n\nSELECTMENU\x10\x13\x12\x10\n\x0cOVERFLOWMENU\x10\x14\x12\x0e\n\nDATEPICKER\x10\x15\x12\x08\n\x04\x44OCS\x10\x16\x12\x06\n\x02H1\x10\x17\x12\x06\n\x02H2\x10\x18\x12\x06\n\x02H3\x10\x19\x12\x06\n\x02UL\x10\x1a\x12\x06\n\x02OL\x10\x1b\x12\x06\n\x02LI\x10\x1c\x12\t\n\x05QUOTE\x10\x1d\x12\x08\n\x04\x43ODE\x10\x1e\x12\x0e\n\nCODE_BLOCK\x10\x1f\x12\x06\n\x02HR\x10 \x12\x0e\n\nTIMEPICKER\x10!\x12\x12\n\x0e\x44\x41TETIMEPICKER\x10\"\x12\x0c\n\x08REACTION\x10#\x12\x0b\n\x07MENTION\x10$\"G\n\x0cTextProperty\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x0f\n\x07i18nKey\x18\x02 \x01(\t\x12\x15\n\rnumberOfLines\x18\x03 \x01(\x05\"+\n\rExtendedEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"&\n\nPipeEntity\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\x03\"F\n\x0eVersionPayload\x12#\n\x0cversionRange\x18\x01 \x01(\x0b\x32\r.VersionRange\x12\x0f\n\x07payload\x18\x02 \x01(\x0c\"*\n\x0cVersionRange\x12\r\n\x05start\x18\x01 \x01(\t\x12\x0b\n\x03\x65nd\x18\x02 \x01(\t\"\xf7\x12\n\x04\x43hat\x12\n\n\x02id\x18\x01 \x01(\t\x12\x18\n\x04type\x18\x02 \x01(\x0e\x32\n.Chat.Type\x12\x15\n\rlastMessageId\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0f\n\x07ownerId\x18\x06 \x01(\t\x12\x17\n\x0fnewMessageCount\x18\x07 \x01(\x05\x12\x1c\n\x06status\x18\x08 \x01(\x0e\x32\x0c.Chat.Status\x12\x12\n\nupdateTime\x18\t \x01(\x03\x12\x0b\n\x03key\x18\n \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0b \x01(\t\x12\x13\n\x0bmemberCount\x18\x0c \x01(\x05\x12\x14\n\x0cisDepartment\x18\r \x01(\x08\x12\x10\n\x08isPublic\x18\x0e \x01(\x08\x12\x1b\n\x13lastMessagePosition\x18\x0f \x01(\x05\x12\x11\n\tuserCount\x18\x10 \x01(\x05\x12\x12\n\nnamePinyin\x18\x11 \x01(\t\x12\x12\n\ncreateTime\x18\x12 \x01(\x03\x12\x19\n\x11isCustomerService\x18\x13 \x01(\x08\x12\x18\n\x04role\x18\x14 \x01(\x0e\x32\n.Chat.Role\x12\x14\n\x0cisCustomIcon\x18\x15 \x01(\x08\x12\x1f\n\x17noBadgedNewMessageCount\x18\x16 \x01(\x05\x12\x1c\n\x14offEditGroupChatInfo\x18\x17 \x01(\x08\x12(\n\x0c\x61nnouncement\x18\x18 \x01(\x0b\x32\x12.Chat.Announcement\x12\x10\n\x08tenantId\x18\x19 \x01(\t\x12\x14\n\x0cupdateTimeMs\x18\x1a \x01(\x03\x12\x10\n\x08isRemind\x18\x1b \x01(\x08\x12\x13\n\x0bisDissolved\x18\x1e \x01(\x08\x12\x11\n\tisMeeting\x18\x1f \x01(\x08\x12\x1c\n\x14lastVisibleMessageId\x18  \x01(\t\x12\x14\n\x0clastThreadId\x18! \x01(\t\x12\x16\n\x0enewThreadCount\x18\" \x01(\x05\x12\x1a\n\x12lastThreadPosition\x18# \x01(\x05\x12\x10\n\x08isCrypto\x18$ \x01(\x08\x12\x1e\n\x16noBadgedNewThreadCount\x18% \x01(\x05\x12\x1b\n\x13threadStartPosition\x18& \x01(\x05\x12 \n\x08\x63hatMode\x18\' \x01(\x0e\x32\x0e.Chat.ChatMode\x12\x15\n\risCrossTenant\x18) \x01(\x08\x12\x10\n\x08isTenant\x18* \x01(\x08\x12&\n\x0bsupportView\x18+ \x01(\x0e\x32\x11.Chat.SupportView\x12\x12\n\njoinTimeMs\x18, \x01(\x03\x12\x10\n\x08oncallId\x18- \x01(\x03\x12\"\n\x1alastVisibleMessagePosition\x18. \x01(\x05\x12\'\n\x1flastVisibleMessageNoBadgedCount\x18/ \x01(\x05\x12\x14\n\x0creadPosition\x18\x30 \x01(\x05\x12\x1e\n\x16readPositionBadgeCount\x18\x31 \x01(\x05\x12%\n\x1dlastMessagePositionBadgeCount\x18\x32 \x01(\x05\x12\x17\n\x0f\x65nableWatermark\x18\x33 \x01(\x08\x12\x11\n\tsidebarId\x18\x35 \x01(\t\x12\x0e\n\x06namePy\x18\x64 \x01(\t\x12+\n\ti18nNames\x18\x65 \x03(\x0b\x32\x14.Chat.I18nNamesEntryB\x02\x18\x01\x12\x1e\n\x07i18nInf\x18\x66 \x01(\x0b\x32\r.Chat.I18nInf\x12\x1a\n\x12readThreadPosition\x18g \x01(\x05\x12$\n\x1creadThreadPositionBadgeCount\x18h \x01(\x05\x12$\n\x1clastThreadPositionBadgeCount\x18i \x01(\x05\x12!\n\x19lastVisibleThreadPosition\x18j \x01(\x05\x12\x1b\n\x13lastVisibleThreadId\x18k \x01(\t\x12\x12\n\nisPublicV2\x18m \x01(\x08\x12\x11\n\tallowPost\x18o \x01(\x08\x12\x12\n\nburnedTime\x18p \x01(\x03\x12\x1c\n\x14putChatterApplyCount\x18q \x01(\x05\x12\x12\n\nshowBanner\x18r \x01(\x08\x12\x14\n\x0cisLargeGroup\x18s \x01(\x08\x12 \n\x18\x66irstChatMessagePosition\x18t \x01(\x05\x12\x10\n\x04tags\x18u \x03(\x05\x42\x02\x10\x00\x12\x1f\n\x05\x65xtra\x18v \x03(\x0b\x32\x10.Chat.ExtraEntry\x12\x19\n\x11isSamePageMeeting\x18w \x01(\x08\x12\x1e\n\x16myThreadsReadTimestamp\x18x \x01(\x03\x12\x1e\n\x16myThreadsLastTimestamp\x18y \x01(\x03\x1a\x30\n\x0eI18nNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1ap\n\x0c\x41nnouncement\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x12\n\nupdateTime\x18\x02 \x01(\x03\x12\x14\n\x0clastEditorId\x18\x03 \x01(\t\x12\x0e\n\x06\x64ocUrl\x18\x04 \x01(\t\x12\x15\n\renableOpendoc\x18\x05 \x01(\x08\x1al\n\x07I18nInf\x12/\n\ti18nNames\x18\x01 \x03(\x0b\x32\x1c.Chat.I18nInf.I18nNamesEntry\x1a\x30\n\x0eI18nNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"8\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03P2P\x10\x01\x12\t\n\x05GROUP\x10\x02\x12\x0f\n\x0bTOPIC_GROUP\x10\x03\".\n\x06Status\x12\n\n\x06NORMAL\x10\x00\x12\x0b\n\x07\x41RCHIVE\x10\x01\x12\x0b\n\x07\x44\x45LETED\x10\x02\"I\n\x08\x43hatMode\x12\x15\n\x11UNKNOWN_CHAT_MODE\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\n\n\x06THREAD\x10\x02\x12\r\n\tTHREAD_V2\x10\x03\"f\n\x0bSupportView\x12\x10\n\x0cVIEW_UNKNOWN\x10\x00\x12\x11\n\rVIEW_P2PGROUP\x10\x01\x12\x10\n\x0cVIEW_MEETING\x10\x02\x12\x0f\n\x0bVIEW_THREAD\x10\x03\x12\x0f\n\x0bVIEW_CRYPTO\x10\x04\"@\n\x04Role\x12\n\n\x06IGNORE\x10\x00\x12\n\n\x06MEMBER\x10\x01\x12\x0b\n\x07VISITOR\x10\x02\x12\x13\n\x0fTHREAD_FOLLOWER\x10\x03\"\xff\x02\n\x08\x46\x65\x65\x64\x43\x61rd\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1c\n\x04type\x18\x02 \x01(\x0e\x32\x0e.FeedCard.Type\x12\x12\n\nupdateTime\x18\x03 \x01(\x03\x12\x11\n\tisDelayed\x18\x04 \x01(\x08\x12\x14\n\x0cparentCardId\x18\x05 \x01(\x03\x12\x10\n\x08rankTime\x18\x06 \x01(\x03\x12$\n\x08\x66\x65\x65\x64Type\x18\x07 \x01(\x0e\x32\x12.FeedCard.FeedType\x12\x0e\n\x06imprId\x18\x08 \x01(\t\x12\x14\n\x0cupdateTimeMs\x18\t \x01(\x03\"p\n\x04Type\x12\x10\n\x0cUNKNOWN_TYPE\x10\x00\x12\x08\n\x04\x43HAT\x10\x01\x12\x08\n\x04MAIL\x10\x02\x12\x07\n\x03\x44OC\x10\x03\x12\n\n\x06THREAD\x10\x04\x12\x07\n\x03\x42OX\x10\x05\x12\x0b\n\x07OPENAPP\x10\x06\x12\t\n\x05TOPIC\x10\x07\x12\x0c\n\x08\x41PP_CHAT\x10\x08\"<\n\x08\x46\x65\x65\x64Type\x12\x10\n\x0cTYPE_UNKNOWN\x10\x00\x12\r\n\tTYPE_NEWS\x10\x01\x12\x0f\n\x0bTYPE_SOCIAL\x10\x02*H\n\nSearchType\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x11\n\rSEARCH_TYPE_1\x10\x01\x12\x11\n\rSEARCH_TYPE_2\x10\x02*2\n\x0bPayloadType\x12\x10\n\x0cTYPE_UNKNOWN\x10\x00\x12\x07\n\x03PB2\x10\x01\x12\x08\n\x04JSON\x10\x02*\xe9\x02\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04POST\x10\x02\x12\x08\n\x04\x46ILE\x10\x03\x12\x08\n\x04TEXT\x10\x04\x12\t\n\x05IMAGE\x10\x05\x12\n\n\x06SYSTEM\x10\x06\x12\t\n\x05\x41UDIO\x10\x07\x12\t\n\x05\x45MAIL\x10\x08\x12\x14\n\x10SHARE_GROUP_CHAT\x10\t\x12\x0b\n\x07STICKER\x10\n\x12\x11\n\rMERGE_FORWARD\x10\x0b\x12\x0c\n\x08\x43\x41LENDAR\x10\x0c\x12\x0e\n\nCLOUD_FILE\x10\r\x12\x08\n\x04\x43\x41RD\x10\x0e\x12\t\n\x05MEDIA\x10\x0f\x12\x18\n\x14SHARE_CALENDAR_EVENT\x10\x10\x12\x0b\n\x07HONGBAO\x10\x11\x12\x14\n\x10GENERAL_CALENDAR\x10\x12\x12\x0e\n\nVIDEO_CHAT\x10\x13\x12\x0c\n\x08LOCATION\x10\x14\x12\x1a\n\x16\x43OMMERCIALIZED_HONGBAO\x10\x16\x12\x13\n\x0fSHARE_USER_CARD\x10\x17\x12\x08\n\x04TODO\x10\x18\x12\n\n\x06\x46OLDER\x10\x19\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PUSHMESSAGESREQUEST_MESSAGESENTRY._options = None
  _PUSHMESSAGESREQUEST_MESSAGESENTRY._serialized_options = b'8\001'
  _PUTCHATREQUEST_DOCPERMSENTRY._options = None
  _PUTCHATREQUEST_DOCPERMSENTRY._serialized_options = b'8\001'
  _PUTCHATREQUEST_DOCPERMS2ENTRY._options = None
  _PUTCHATREQUEST_DOCPERMS2ENTRY._serialized_options = b'8\001'
  _PUTCHATREQUEST_DOCPAIR_PERMSENTRY._options = None
  _PUTCHATREQUEST_DOCPAIR_PERMSENTRY._serialized_options = b'8\001'
  _USERFILTER_CUSTOMFIELDSENTRY._options = None
  _USERFILTER_CUSTOMFIELDSENTRY._serialized_options = b'8\001'
  _RICHTEXTELEMENTS_DICTIONARYENTRY._options = None
  _RICHTEXTELEMENTS_DICTIONARYENTRY._serialized_options = b'8\001'
  _RICHTEXTELEMENTS_STYLEREFSENTRY._options = None
  _RICHTEXTELEMENTS_STYLEREFSENTRY._serialized_options = b'8\001'
  _RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS.fields_by_name['styleIds']._options = None
  _RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS.fields_by_name['styleIds']._serialized_options = b'\020\000'
  _RICHTEXTELEMENT_STYLEENTRY._options = None
  _RICHTEXTELEMENT_STYLEENTRY._serialized_options = b'8\001'
  _CHAT_I18NNAMESENTRY._options = None
  _CHAT_I18NNAMESENTRY._serialized_options = b'8\001'
  _CHAT_EXTRAENTRY._options = None
  _CHAT_EXTRAENTRY._serialized_options = b'8\001'
  _CHAT_I18NINF_I18NNAMESENTRY._options = None
  _CHAT_I18NINF_I18NNAMESENTRY._serialized_options = b'8\001'
  _CHAT.fields_by_name['i18nNames']._options = None
  _CHAT.fields_by_name['i18nNames']._serialized_options = b'\030\001'
  _CHAT.fields_by_name['tags']._options = None
  _CHAT.fields_by_name['tags']._serialized_options = b'\020\000'
  _globals['_SEARCHTYPE']._serialized_start=12720
  _globals['_SEARCHTYPE']._serialized_end=12792
  _globals['_PAYLOADTYPE']._serialized_start=12794
  _globals['_PAYLOADTYPE']._serialized_end=12844
  _globals['_TYPE']._serialized_start=7531
  _globals['_TYPE']._serialized_end=7892
  _globals['_FRAME']._serialized_start=16
  _globals['_FRAME']._serialized_end=182
  _globals['_PACKET']._serialized_start=185
  _globals['_PACKET']._serialized_end=470
  _globals['_PUSHMESSAGESREQUEST']._serialized_start=473
  _globals['_PUSHMESSAGESREQUEST']._serialized_end=616
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._serialized_start=550
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._serialized_end=616
  _globals['_PUTMESSAGEREQUEST']._serialized_start=619
  _globals['_PUTMESSAGEREQUEST']._serialized_end=910
  _globals['_PUTCHATREQUEST']._serialized_start=913
  _globals['_PUTCHATREQUEST']._serialized_end=1623
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._serialized_start=1286
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._serialized_end=1362
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._serialized_start=1364
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._serialized_end=1437
  _globals['_PUTCHATREQUEST_DOCPAIR']._serialized_start=1440
  _globals['_PUTCHATREQUEST_DOCPAIR']._serialized_end=1575
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._serialized_start=1502
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._serialized_end=1575
  _globals['_PUTCHATREQUEST_DOCPERMTYPE']._serialized_start=1577
  _globals['_PUTCHATREQUEST_DOCPERMTYPE']._serialized_end=1623
  _globals['_PUTCHATRESPONSE']._serialized_start=1625
  _globals['_PUTCHATRESPONSE']._serialized_end=1692
  _globals['_UNIVERSALSEARCHREQUEST']._serialized_start=1694
  _globals['_UNIVERSALSEARCHREQUEST']._serialized_end=1762
  _globals['_UNIVERSALSEARCHRESPONSE']._serialized_start=1765
  _globals['_UNIVERSALSEARCHRESPONSE']._serialized_end=4068
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHRESULT']._serialized_start=2053
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHRESULT']._serialized_end=2264
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER']._serialized_start=2267
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER']._serialized_end=3182
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO']._serialized_start=2600
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO']._serialized_end=3070
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_TIMEUNIT']._serialized_start=2895
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_TIMEUNIT']._serialized_end=2962
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_HASMOREINFO']._serialized_start=2964
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_HASMOREINFO']._serialized_end=3070
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_INVOKEABNORMALNOTICE']._serialized_start=3072
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_INVOKEABNORMALNOTICE']._serialized_end=3182
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHEXTRAFIELDS']._serialized_start=3184
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHEXTRAFIELDS']._serialized_end=3238
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO']._serialized_start=3241
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO']._serialized_end=3733
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO_SEARCHENTITYTYPE']._serialized_start=3401
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO_SEARCHENTITYTYPE']._serialized_end=3733
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHENTITYTYPE']._serialized_start=3401
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHENTITYTYPE']._serialized_end=3733
  _globals['_SEARCHCOMMONREQUESTHEADER']._serialized_start=4071
  _globals['_SEARCHCOMMONREQUESTHEADER']._serialized_end=4546
  _globals['_SEARCHCOMMONREQUESTHEADER_LAYOUT']._serialized_start=4509
  _globals['_SEARCHCOMMONREQUESTHEADER_LAYOUT']._serialized_end=4546
  _globals['_SEARCHEXTRAPARAM']._serialized_start=4548
  _globals['_SEARCHEXTRAPARAM']._serialized_end=4625
  _globals['_BASEENTITY']._serialized_start=4628
  _globals['_BASEENTITY']._serialized_end=4764
  _globals['_BASEENTITY_SEARCHCONTEXT']._serialized_start=4642
  _globals['_BASEENTITY_SEARCHCONTEXT']._serialized_end=4764
  _globals['_COMMONFILTER']._serialized_start=4766
  _globals['_COMMONFILTER']._serialized_end=4824
  _globals['_USERFILTER']._serialized_start=4827
  _globals['_USERFILTER']._serialized_end=5059
  _globals['_USERFILTER_FIELDVALUES']._serialized_start=4952
  _globals['_USERFILTER_FIELDVALUES']._serialized_end=4981
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._serialized_start=4983
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._serialized_end=5059
  _globals['_GROUPCHATFILTER']._serialized_start=5062
  _globals['_GROUPCHATFILTER']._serialized_end=5206
  _globals['_ENTITYITEM']._serialized_start=5209
  _globals['_ENTITYITEM']._serialized_end=5800
  _globals['_ENTITYITEM_ENTITYFILTER']._serialized_start=5361
  _globals['_ENTITYITEM_ENTITYFILTER']._serialized_end=5465
  _globals['_ENTITYITEM_SEARCHENTITYTYPE']._serialized_start=3401
  _globals['_ENTITYITEM_SEARCHENTITYTYPE']._serialized_end=3733
  _globals['_CONTENT']._serialized_start=5803
  _globals['_CONTENT']._serialized_end=6351
  _globals['_CONTENT_FILETRANSMODE']._serialized_start=6308
  _globals['_CONTENT_FILETRANSMODE']._serialized_end=6351
  _globals['_ENTITIES']._serialized_start=6354
  _globals['_ENTITIES']._serialized_end=8244
  _globals['_ENTITIES_MESSAGE']._serialized_start=6367
  _globals['_ENTITIES_MESSAGE']._serialized_end=7892
  _globals['_ENTITIES_MESSAGE_TYPE']._serialized_start=7531
  _globals['_ENTITIES_MESSAGE_TYPE']._serialized_end=7892
  _globals['_ENTITIES_FROMTYPE']._serialized_start=7894
  _globals['_ENTITIES_FROMTYPE']._serialized_end=7945
  _globals['_ENTITIES_STATUS']._serialized_start=7947
  _globals['_ENTITIES_STATUS']._serialized_end=8014
  _globals['_ENTITIES_REMOVERTYPE']._serialized_start=8016
  _globals['_ENTITIES_REMOVERTYPE']._serialized_end=8100
  _globals['_ENTITIES_MESSAGESENSITIVITY']._serialized_start=8102
  _globals['_ENTITIES_MESSAGESENSITIVITY']._serialized_end=8172
  _globals['_ENTITIES_CHATTYPE']._serialized_start=8174
  _globals['_ENTITIES_CHATTYPE']._serialized_end=8244
  _globals['_TEXTCONTENT']._serialized_start=8246
  _globals['_TEXTCONTENT']._serialized_end=8302
  _globals['_RICHTEXT']._serialized_start=8305
  _globals['_RICHTEXT']._serialized_end=8556
  _globals['_RICHTEXTELEMENTS']._serialized_start=8559
  _globals['_RICHTEXTELEMENTS']._serialized_end=8993
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._serialized_start=8736
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._serialized_end=8803
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._serialized_start=8805
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._serialized_end=8897
  _globals['_RICHTEXTELEMENTS_RICHTEXTSTYLE']._serialized_start=8899
  _globals['_RICHTEXTELEMENTS_RICHTEXTSTYLE']._serialized_end=8943
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS']._serialized_start=8945
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS']._serialized_end=8993
  _globals['_RICHTEXTELEMENT']._serialized_start=8996
  _globals['_RICHTEXTELEMENT']._serialized_end=9632
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._serialized_start=9149
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._serialized_end=9193
  _globals['_RICHTEXTELEMENT_TAG']._serialized_start=9196
  _globals['_RICHTEXTELEMENT_TAG']._serialized_end=9632
  _globals['_TEXTPROPERTY']._serialized_start=9634
  _globals['_TEXTPROPERTY']._serialized_end=9705
  _globals['_EXTENDEDENTRY']._serialized_start=9707
  _globals['_EXTENDEDENTRY']._serialized_end=9750
  _globals['_PIPEENTITY']._serialized_start=9752
  _globals['_PIPEENTITY']._serialized_end=9790
  _globals['_VERSIONPAYLOAD']._serialized_start=9792
  _globals['_VERSIONPAYLOAD']._serialized_end=9862
  _globals['_VERSIONRANGE']._serialized_start=9864
  _globals['_VERSIONRANGE']._serialized_end=9906
  _globals['_CHAT']._serialized_start=9909
  _globals['_CHAT']._serialized_end=12332
  _globals['_CHAT_I18NNAMESENTRY']._serialized_start=11663
  _globals['_CHAT_I18NNAMESENTRY']._serialized_end=11711
  _globals['_CHAT_EXTRAENTRY']._serialized_start=11713
  _globals['_CHAT_EXTRAENTRY']._serialized_end=11757
  _globals['_CHAT_ANNOUNCEMENT']._serialized_start=11759
  _globals['_CHAT_ANNOUNCEMENT']._serialized_end=11871
  _globals['_CHAT_I18NINF']._serialized_start=11873
  _globals['_CHAT_I18NINF']._serialized_end=11981
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._serialized_start=11663
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._serialized_end=11711
  _globals['_CHAT_TYPE']._serialized_start=11983
  _globals['_CHAT_TYPE']._serialized_end=12039
  _globals['_CHAT_STATUS']._serialized_start=12041
  _globals['_CHAT_STATUS']._serialized_end=12087
  _globals['_CHAT_CHATMODE']._serialized_start=12089
  _globals['_CHAT_CHATMODE']._serialized_end=12162
  _globals['_CHAT_SUPPORTVIEW']._serialized_start=12164
  _globals['_CHAT_SUPPORTVIEW']._serialized_end=12266
  _globals['_CHAT_ROLE']._serialized_start=12268
  _globals['_CHAT_ROLE']._serialized_end=12332
  _globals['_FEEDCARD']._serialized_start=12335
  _globals['_FEEDCARD']._serialized_end=12718
  _globals['_FEEDCARD_TYPE']._serialized_start=12544
  _globals['_FEEDCARD_TYPE']._serialized_end=12656
  _globals['_FEEDCARD_FEEDTYPE']._serialized_start=12658
  _globals['_FEEDCARD_FEEDTYPE']._serialized_end=12718
# @@protoc_insertion_point(module_scope)
