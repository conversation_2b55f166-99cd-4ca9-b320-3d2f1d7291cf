import re
import time
import os
import subprocess
import json
from urllib.parse import urlencode

import asyncio

import blackboxprotobuf
import static.proto_pb2 as FLY_BOOK_PROTO
from FlyBookApi import FlyBookApi
import websockets

from builder.auth import FlyBookAuth
from builder.params import ParamsBuilder
from builder.proto import ProtoBuilder


class FlyBookLive:
    def __init__(self, auth):
        self.base_url = 'wss://msg-frontier.feishu.cn/ws/v2'
        self.auth = auth
        self.fly_book_api = FlyBookApi()
        _, x_csrf_token = self.fly_book_api.get_csrf_token(self.auth)
        _, self.me_id = self.fly_book_api.get_user_info(self.auth, x_csrf_token)
        self.me_id = str(self.me_id)
        self.ws = None
        # 记录已处理的聊天ID，避免重复处理
        self.processed_chats = set()
        # 脚本路径
        self.script_path = r"D:\个人开发\OpenFeiShuApis-master3.0\simple_send_message.py"

    def check_customer_service_pattern(self, text):
        """检测客服接通消息模式"""
        # 更精确的模式，确保包含所有必要元素
        pattern = r'已接通人工.*?@.*?为你服务.*?请问.*?帮你.*?'
        return re.search(pattern, text, re.DOTALL) is not None

    def update_script_chat_id(self, chat_id):
        """更新simple_send_message.py中的chat_id"""
        try:
            # 读取脚本文件
            with open(self.script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换chat_id
            pattern = r'chat_id = "[^"]*"'
            replacement = f'chat_id = "{chat_id}"'
            new_content = re.sub(pattern, replacement, content)

            # 写回文件
            with open(self.script_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            print(f"已更新脚本中的chat_id为: {chat_id}")
            return True
        except Exception as e:
            print(f"更新脚本失败: {str(e)}")
            return False

    async def run_send_message_script(self, chat_id):
        """运行发送消息脚本"""
        try:
            # 更新脚本中的chat_id
            if not self.update_script_chat_id(chat_id):
                return False

            # 运行脚本
            print(f"正在运行发送消息脚本，目标聊天ID: {chat_id}")

            # 使用asyncio运行子进程
            process = await asyncio.create_subprocess_exec(
                'python', self.script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.path.dirname(self.script_path)
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                print("脚本执行成功")
                print(f"输出: {stdout.decode('utf-8', errors='ignore')}")
                return True
            else:
                print(f"脚本执行失败，返回码: {process.returncode}")
                print(f"错误: {stderr.decode('utf-8', errors='ignore')}")
                return False

        except Exception as e:
            print(f"运行脚本时出错: {str(e)}")
            return False

    async def send_ack(self, ws, Packet_sid):
        payload = FLY_BOOK_PROTO.Packet()
        payload.cmd = 1
        payload.payloadType = 1
        payload.sid = Packet_sid
        payload = payload.SerializeToString()
        Frame = FLY_BOOK_PROTO.Frame()
        current = int(time.time() * 1000)
        Frame.seqid = current
        Frame.logid = current
        Frame.service = 1
        Frame.method = 1
        ExtendedEntry = FLY_BOOK_PROTO.ExtendedEntry()
        ExtendedEntry.key = 'x-request-time'
        ExtendedEntry.value = f'{current}000'
        Frame.headers.append(ExtendedEntry)
        Frame.payloadType = "pb"
        Frame.payload = payload
        Frame = Frame.SerializeToString()
        await ws.send(Frame)

    async def main(self):
        params = ParamsBuilder.build_receive_msg_param(auth).get()
        url = f"{self.base_url}?{urlencode(params)}"
        async with websockets.connect(url) as websocket:
            async for message in websocket:
                try:
                    _, Packet_sid, fromId, MessageChatId, ReceiveTextContent = ProtoBuilder.decode_receive_msg_proto(message)
                    if MessageChatId is not None:
                        print(f"fromId: {fromId}")
                        print(f"ChatId: {MessageChatId}")
                        print(f"ReceiveTextContent: {ReceiveTextContent}")

                        # 检测客服接通消息
                        if self.check_customer_service_pattern(ReceiveTextContent):
                            print("检测到客服接通消息!")
                            print(f"消息内容: {ReceiveTextContent}")

                            # 避免重复处理同一个聊天
                            if MessageChatId not in self.processed_chats:
                                self.processed_chats.add(MessageChatId)
                                print(f"开始处理聊天ID: {MessageChatId}")

                                # 异步运行发送消息脚本
                                success = await self.run_send_message_script(MessageChatId)
                                if success:
                                    print(f"成功处理聊天ID: {MessageChatId}")
                                else:
                                    print(f"处理聊天ID失败: {MessageChatId}")
                            else:
                                print(f"聊天ID {MessageChatId} 已经处理过，跳过")

                        # 原有的消息转发逻辑
                        if not str(fromId) == self.me_id:
                            try:
                                if self.me_id in ReceiveTextContent:
                                    to_who = re.findall(r'给\$(.*?)\$', ReceiveTextContent)[0]
                                    send_text = re.findall(r'发送\$(.*?)\$', ReceiveTextContent)[0]
                                    _, userAndGroupIds = self.fly_book_api.search_some(self.auth, to_who)
                                    user_or_group_id = userAndGroupIds[0]
                                    if user_or_group_id['type'] == 'user':
                                        userId = user_or_group_id['id']
                                        _, chatId = self.fly_book_api.create_chat(self.auth, userId)
                                    else:
                                        chatId = user_or_group_id['id']
                                    self.fly_book_api.send_msg(self.auth, send_text, chatId)
                                else:
                                    raise Exception
                            except Exception as e:
                                pass
                                # send_text = '我是一个机器人，如果你想和我聊天，请@我并发送消息，消息格式：给$用户名$发送$消息内容$'
                                # self.fly_book_api.send_msg(self.auth, send_text, MessageChatId)

                    await self.send_ack(websocket, Packet_sid)
                    print('==============================')
                except Exception as e:
                    print(str(e))


if __name__ == '__main__':
    auth = FlyBookAuth()
    cookie_str = 'passport_web_did=7507138464031457299; passport_trace_id=7507138464048021523; QXV0aHpDb250ZXh0=92a3a31bc0854c9cb734905143cc1fd3; _gcl_au=1.1.*********.**********; __tea__ug__uid=7507138400685032999; s_v_web_id=verify_mayxp6dz_64Armn4F_zkfb_4KDI_96tJ_JtwnHkZbe196; is_anonymous_session=; lang=zh; _csrf_token=95c77a808df924312c6583c08d7e035b0e76fe13-**********; help_center_session=********-2fa8-4c0f-91a3-8da84349bd05; _uuid_hera_ab_path_1=7507625345856798723; i18n_locale=zh-CN; lgw_csrf_token=19ce048f7d4385d24a120ad7453476bd13707bc2-**********; locale=zh-CN; _gid=GA1.2.**********.**********; login_recently=1; lob_csrf_token=e8c90a6e4f8858842e228531b5b01b93; landing_url=https://accounts.feishu.cn/accounts/page/login?app_id=7&force_login=1&no_trap=1&redirect_uri=https%3A%2F%2Fopen.feishu.cn%2Fapp; session=XN0YXJ0-fder417d-7e36-4113-ad61-30248fb2c32b-WVuZA; session_list=XN0YXJ0-6bcv25ec-1910-4aea-b4ce-9911867afb0a-WVuZA_XN0YXJ0-fder417d-7e36-4113-ad61-30248fb2c32b-WVuZA; passport_app_access_token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************.6aGrEmaMBA6PaajRjI4MIVU5UtW-rXfa4oC-BCqa5JhGFoEXTpOB748OhUJT9hRwwdOrqhpFHLHwkRyRIHye2w; Hm_lvt_89b2f04167e2aa91e52105f43f897c29=**********,**********,**********; HMACCOUNT=B0199BD78497EA54; Hm_lpvt_89b2f04167e2aa91e52105f43f897c29=**********; Hm_lvt_a79616d9322d81f12a92402ac6ae32ea=**********,**********,**********,**********; Hm_lpvt_a79616d9322d81f12a92402ac6ae32ea=**********; _uetsid=ed09c5a03eb411f09f78cbbcc18adc3c; _uetvid=f0295e4037d511f0bee297306b458456; _ga_VPYRHN104D=GS2.1.s1748760724$o12$g1$t1748760724$j60$l0$h0; _ga=GA1.2.**********.**********; x_lsc_cac_token=eyJhbGciOiJFUzI1NiIsImZlYXR1cmVfY29kZSI6ImxzbV9jb25kX2FjY2VzcyIsImtpZCI6Ijc1MDk2NTExNjY4NTU2MTAzNzAiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************.o3K3DZRcgT-mnAK6TtA6j50cdTRPG_Gjc8c4Cu1U6ahvTj88Ko8yd9gsPri3HmOHrI472tcAMH3aVkaUhpKPwQ; sl_session=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.t9uRMsCLE11tPqC65O-SBbr6_f8tdEObuGcWHNTOUw8lyx3__KcPshsX1GARYxVJ1nasLLOpCY4CIzTtUoKCDA; swp_csrf_token=130886a4-7145-44d9-a417-2e934a56a694; t_beda37=cd94243c9651d410b7e3558d13c93a61f0d8c933725f478e936800a17951ef47; msToken=luuQJIJzt-dp7QZ0MAlkLOaLFlPxeZFsAzVjWWu-i2r2koZeNTkJHaoQW_jeOxChHnhZgh7n8N5xkXr0x2ShBv_qw3EfjH57Z-C-v-TIFTBSRRw-0ocSBhfRDNCgtj0RmmPOkFb4hnhSvuFezXNN7hp7YHLnO8YBfjQKaA8Tko8FrOzMsOkWew=='
    auth.perepare_auth(cookie_str)

    live = FlyBookLive(auth)
    asyncio.run(live.main())