syntax = "proto3";

message Frame {
    uint64 seqid = 1;
    uint64 logid = 2;
    int32 service = 3;
    int32 method = 4;
    repeated ExtendedEntry headers = 5;
    string payloadEncoding = 6;
    string payloadType = 7;
    bytes payload = 8;
}

message Packet {
    string sid = 1;
    PayloadType payloadType = 2;
    int32 cmd = 3;
    uint32 status = 4;
    bytes payload = 5;
    string cid = 6;
    PipeEntity pipeEntity = 7;
    repeated VersionPayload versionPayloads = 8;
    repeated PipeEntity pipeEntities = 9;
    uint32 waitRetryInterval = 10;
    int32 command = 11;
    uint64 cursor = 12;
}

message PushMessagesRequest {
    map<string, entities.Message> messages = 1;
    map<string, bool> participatedMessageIds = 3;
    map<string, bool> forcePush = 8;
    map<string, bool> messagesAtMe = 9;
}

message PutMessageRequest {
    Type type = 1;
    Content content = 2;
    string chatId = 3;
    string rootId = 4;
    string parentId = 5;
    string cid = 6;
    bool isNotified = 7;
    bool sendToChat = 8;
    int32 version = 9;
    bool isThreadGroupTopic = 10;
    bool isAnonymous = 11;
    repeated string leftStaticResourceKeys = 101;
    bytes thriftMessage = 102;
}


message PutChatRequest {
    Type type = 1;
    repeated string userIds = 2;
    string groupName = 3;
    string groupDesc = 4;
    bool isPublic = 5;
    repeated string chatterIds = 6;
    string organizationId = 7;
    string fromChatId = 8;
    repeated string initMessageIds = 9;
    string iconKey = 10;
    map<string, DocPermType> docPerms = 11;
    bool isCrossTenant = 12;
    bool isPublicV2 = 13;
    map<string, DocPair> docPerms2 = 15;
    string cid = 16;

    enum DocPermType {
        UNKNOWN = 0;
        READ = 1;
        EDIT = 2;
    }

    message DocPair {
        map<string, DocPermType> perms = 1;
    }
}

message PutChatResponse {
    Chat chat = 1;
    FeedCard feedCard = 2;
}

message UniversalSearchRequest {
    SearchCommonRequestHeader header = 1;
}



message UniversalSearchResponse {
    SearchCommonResponseHeader header = 1;
    repeated SearchResult results = 2;
    SearchExtraFields extraFields = 3;
    repeated FailedEntityInfo failedEntityInfos = 5;


    message SearchResult {
        string id = 1;
        SearchEntityType type = 2;
        string titleHighlighted = 3;
        string summaryHighlighted = 4;
        string extrasHighlighted = 5;
        string avatarKey = 6;
        string extraInfoSeparator = 10;
    }

    enum SearchEntityType {
        UNKNOWN = 0;
        USER = 1;
        BOT = 2;
        GROUP_CHAT = 3;
        CRYPTO_P2P_CHAT = 4;
        MESSAGE = 5;
        DOC = 7;
        WIKI = 8;
        APP = 9;
        ONCALL = 10;
        THREAD = 11;
        QA_CARD = 12;
        URL = 13;
        DEPARTMENT = 14;
        PANO = 15;
        SLASH_COMMAND = 16;
        SECTION = 17;
        RESOURCE = 18;
        CUSTOMIZATION = 19;
        FACILITY = 20;
        MAIL_CONTACT = 21;
        CHAMELEON = 22;
        CALENDAR_EVENT = 23;
    }


    message SearchCommonResponseHeader {
        string searchSession = 1;
        int32 sessionSeqId = 2;
        int32 total = 3;
        bool hasMore = 4;
        string paginationToken = 5;
        InvokeAbnormalNotice invokeAbnormalNotice = 6;
        ColdAndHotStorageInfo storageInfo = 7;

        enum InvokeAbnormalNotice {
          INVOKE_NORMAL = 0;
          REQUEST_CANCELED = 1;
          VERSION_SWITCH = 2;
          QUERY_LENGTH_EXCEEDED = 3;
        }

        message ColdAndHotStorageInfo {
            bool isNeedColdData = 1;
            int32 timeSize = 2;
            TimeUnit timeUnit = 3;
            string timeText = 4;
            HasMoreInfo hasMoreInfo = 5;

            enum TimeUnit {
                TimeUNKNOWN = 0;
                DAY = 1;
                WEEK = 2;
                MONTH = 3;
                YEAR = 4;
            }
            enum HasMoreInfo {
                UNKNOWN = 0;
                HOT_HAS_MORE = 1;
                HOT_HAS_NO_MORE = 2;
                COLD_HAS_MORE = 3;
                COLD_HAS_NO_MORE = 4;
            }
        }
    }

    message SearchExtraFields {
        bytes chatterPermissionResponse = 1;
    }

    message FailedEntityInfo {
        SearchEntityType entityType = 1;
        bool isNeedLocalFallback = 2;
        string localPaginationToken = 3;

        enum SearchEntityType {
            UNKNOWN = 0;
            USER = 1;
            BOT = 2;
            GROUP_CHAT = 3;
            CRYPTO_P2P_CHAT = 4;
            MESSAGE = 5;
            DOC = 7;
            WIKI = 8;
            APP = 9;
            ONCALL = 10;
            THREAD = 11;
            QA_CARD = 12;
            URL = 13;
            DEPARTMENT = 14;
            PANO = 15;
            SLASH_COMMAND = 16;
            SECTION = 17;
            RESOURCE = 18;
            CUSTOMIZATION = 19;
            FACILITY = 20;
            MAIL_CONTACT = 21;
            CHAMELEON = 22;
            CALENDAR_EVENT = 23;
        }
    }
}

message SearchCommonRequestHeader {
    string searchSession = 1;
    int32 sessionSeqId = 2;
    string query = 3;
    string paginationToken = 4;
    BaseEntity.SearchContext searchContext = 5;
    string locale = 6;
    string impressionId = 7;
    SearchExtraParam extraParam = 8;
    Layout titleLayout = 9;
    Layout summaryLayout = 10;
    int32 pageSize = 11;
    Layout sectionSummaryLayout = 13;
    message Layout {
        int32 line = 1;
        int32 width = 2;
    }
}
message SearchExtraParam {
    bytes chatterPermissionRequest = 1;
    int32 queryInputState = 2;
}
message BaseEntity {
    message SearchContext {
        string tagName = 1;
        repeated EntityItem entityItems = 2;
        CommonFilter commonFilter = 3;
        string sourceKey = 5;
    }
}

message CommonFilter {
    bool includeOuterTenant = 1;
    string chatId = 2;
}
message UserFilter {
    message FieldValues {
        repeated string values = 1;
    }
    bool isResigned = 1;
    bool haveChatter = 2;
    map<string, FieldValues> customFields = 3;
    bool exclude = 4;
}
message GroupChatFilter {
    repeated SearchType searchTypes = 1;
    repeated string chatMemberIds = 2;
    repeated string excludedChatIds = 3;
    bool searchCrypto = 4;
    bool addableAsUser = 5;
}

enum SearchType {
    TYPE_UNSPECIFIED = 0;
    SEARCH_TYPE_1 = 1;
    SEARCH_TYPE_2 = 2;
}
message EntityItem {
    SearchEntityType type = 1;
    EntityFilter filter = 2;
    repeated string boostChatIds = 4;
    string localPaginationToken = 5;

    message EntityFilter {
        oneof filter {
          UserFilter userFilter = 1;
          GroupChatFilter groupChatFilter = 2;
        }
    }
    enum SearchEntityType {
        UNKNOWN = 0;
        USER = 1;
        BOT = 2;
        GROUP_CHAT = 3;
        CRYPTO_P2P_CHAT = 4;
        MESSAGE = 5;
        DOC = 7;
        WIKI = 8;
        APP = 9;
        ONCALL = 10;
        THREAD = 11;
        QA_CARD = 12;
        URL = 13;
        DEPARTMENT = 14;
        PANO = 15;
        SLASH_COMMAND = 16;
        SECTION = 17;
        RESOURCE = 18;
        CUSTOMIZATION = 19;
        FACILITY = 20;
        MAIL_CONTACT = 21;
        CHAMELEON = 22;
        CALENDAR_EVENT = 23;
    }
}

message Content {
    string text = 1;
    string imageKey = 2;
    bool isOriginSource = 31;
    string title = 3;
    repeated string attachments = 4;
    bool isNotified = 5;
    string audioKey = 7;
    int32 audioDuration = 8;
    string chatId = 9;
    string cryptoToken = 10;
    string fileKey = 6;
    string fileName = 11;
    string fileMime = 12;
    int64 fileSize = 13;
    FileTransMode fileTransMode = 28;
    string senderDeviceId = 29;
    RichText richText = 14;
    int32 duration = 15;
    int32 attendeesCount = 17;
    bool isGroupAnnouncement = 18;
    string stickerSetId = 24;
    string stickerId = 25;
    string shareUserId = 27;

    enum FileTransMode {
        UNKNOWN = 0;
        LAN_TRANS = 1;
    }
}
message entities {
    message Message {
        string id = 1;
        Type type = 2;
        string fromId = 3;
        int64 createTime = 4;
        bytes content = 5;
        Status status = 6;
        FromType fromType = 7;
        string rootId = 8;
        string parentId = 9;
        string chatId = 10;
        int64 lastModifyTime = 11;
        string cid = 12;
        int32 position = 13;
        int64 updateTime = 14;
        bool isNotified = 15;
        string replyCount = 16;
        string parentSourceMessageId = 17;
        string rootSourceMessageId = 18;
        bool isDing = 19;
        string threadId = 20;
        bool sendToChat = 21;
        bool isTruncated = 22;
        bool isRemoved = 23;
        string channelId = 24;
        int32 threadPosition = 28;
        int64 removerId = 29;
        string translateLanguage = 30;
        RemoverType removerType = 31;
        int32 noBadgedCount = 33;
        bool isBadged = 34;
        int32 badgeCount = 35;
        int32 threadBadgeCount = 36;
        int32 threadReplyCount = 37;
        repeated string atOutChatterIds = 38;
        string messageLanguage = 39;
        bool isNoTraceRemoved = 41;
        bool isAutoTranslatedByReceiver = 42;
        MessageSensitivity sensitivity = 43;
        bool isVisibleV2 = 44;
        ChatType chatType = 46;
        string originalSenderId = 47;
        bool isStaticResourceMessageDeleted = 48;
        int64 messagePipeVersion = 52;
        bool isBatchCopyMessages = 53;
        bool isSpecialFocus = 56;
        bool isIncludeDocUrl = 58;
        int64 cipherId = 59;
        enum Type {
            UNKNOWN = 0;
            POST = 2;
            FILE = 3;
            TEXT = 4;
            IMAGE = 5;
            SYSTEM = 6;
            AUDIO = 7;
            EMAIL = 8;
            SHARE_GROUP_CHAT = 9;
            STICKER = 10;
            MERGE_FORWARD = 11;
            CALENDAR = 12;
            CLOUD_FILE = 13;
            CARD = 14;
            MEDIA = 15;
            SHARE_CALENDAR_EVENT = 16;
            HONGBAO = 17;
            GENERAL_CALENDAR = 18;
            VIDEO_CHAT = 19;
            LOCATION = 20;
            COMMERCIALIZED_HONGBAO = 22;
            SHARE_USER_CARD = 23;
            TODO = 24;
            FOLDER = 25;
        }
    }


    enum FromType {
        UNKNOWN_FROMTYPE = 0;
        USER = 1;
        BOT = 2;
    }

    enum Status {
        UNKNOWN_STATUS = 0;
        NORMAL = 1;
        DELETED = 2;
        MODIFIED = 3;
    }

    enum RemoverType {
        UNKNOWN_REMOVERTYPE = 0;
        GROUPOWNER = 1;
        SYSADMIN = 2;
        GROUPADMIN = 3;
    }

    enum MessageSensitivity {
        UNKNOWN_SENSITIVITY = 0;
        SAFE = 1;
        DANGEROUS = 2;
    }

    enum ChatType {
        UNKNOWN_CHAT_TYPE = 0;
        P2P = 1;
        GROUP = 2;
        TOPIC_GROUP = 3;
    }

}

message TextContent {
    string text = 1;
    RichText richText = 3;
}

message RichText {
    repeated string elementIds = 1;
    string innerText = 2;
    RichTextElements elements = 3;
    repeated string imageIds = 5;
    repeated string atIds = 6;
    repeated string anchorIds = 7;
    repeated string i18nIds = 8;
    repeated string mediaIds = 9;
    repeated string docsIds = 10;
    repeated string interactiveIds = 11;
    repeated string mentionIds = 12;
    int32 version = 13;
}


message RichTextElements {
    map<string, RichTextElement> dictionary = 1;
    map<string, RichTextElementStyleRefs> styleRefs = 2;
    repeated RichTextStyle styles = 3;

    message RichTextStyle {
        string name = 1;
        string value = 2;
    }

    message RichTextElementStyleRefs {
        repeated int32 styleIds = 1 [packed = false];
    }
}


message RichTextElement {
    Tag tag = 1;
    map<string, string> style = 2;
    bytes property = 3;
    repeated string childIds = 4;
    repeated string styleKeys = 5;
    enum Tag {
        UNKNOWN_TAG = 0;
        TEXT = 1;
        IMG = 2;
        P = 3;
        FIGURE = 4;
        AT = 5;
        A = 6;
        B = 7;
        I = 8;
        U = 9;
        EMOTION = 10;
        BUTTON = 11;
        SELECT = 12;
        PROGRESS_SELECT_OPTION = 13;
        DIV = 14;
        TEXTABLE_AREA = 15;
        TIME = 16;
        LINK = 17;
        MEDIA = 18;
        SELECTMENU = 19;
        OVERFLOWMENU = 20;
        DATEPICKER = 21;
        DOCS = 22;
        H1 = 23;
        H2 = 24;
        H3 = 25;
        UL = 26;
        OL = 27;
        LI = 28;
        QUOTE = 29;
        CODE = 30;
        CODE_BLOCK = 31;
        HR = 32;
        TIMEPICKER = 33;
        DATETIMEPICKER = 34;
        REACTION = 35;
        MENTION = 36;
    }
}


message TextProperty {
    string content = 1;
    string i18nKey = 2;
    int32 numberOfLines = 3;
}
message ExtendedEntry {
    string key = 1;
    string value = 2;
}


enum PayloadType {
    TYPE_UNKNOWN = 0;
    PB2 = 1;
    JSON = 2;
}

message PipeEntity {
    string type = 1;
    int64 id = 3;
}

message VersionPayload {
    VersionRange versionRange = 1;
    bytes payload = 2;
}

message VersionRange {
    string start = 1;
    string end = 2;
}

enum Type {
    UNKNOWN = 0;
    POST = 2;
    FILE = 3;
    TEXT = 4;
    IMAGE = 5;
    SYSTEM = 6;
    AUDIO = 7;
    EMAIL = 8;
    SHARE_GROUP_CHAT = 9;
    STICKER = 10;
    MERGE_FORWARD = 11;
    CALENDAR = 12;
    CLOUD_FILE = 13;
    CARD = 14;
    MEDIA = 15;
    SHARE_CALENDAR_EVENT = 16;
    HONGBAO = 17;
    GENERAL_CALENDAR = 18;
    VIDEO_CHAT = 19;
    LOCATION = 20;
    COMMERCIALIZED_HONGBAO = 22;
    SHARE_USER_CARD = 23;
    TODO = 24;
    FOLDER = 25;
}

message Chat {
    string id = 1;
    Type type = 2;
    string lastMessageId = 3;
    string name = 4;
    string ownerId = 6;
    int32 newMessageCount = 7;
    Status status = 8;
    int64 updateTime = 9;
    string key = 10;
    string description = 11;
    int32 memberCount = 12;
    bool isDepartment = 13;
    bool isPublic = 14;
    int32 lastMessagePosition = 15;
    int32 userCount = 16;
    string namePinyin = 17;
    int64 createTime = 18;
    bool isCustomerService = 19;
    Role role = 20;
    bool isCustomIcon = 21;
    int32 noBadgedNewMessageCount = 22;
    bool offEditGroupChatInfo = 23;
    Announcement announcement = 24;
    string tenantId = 25;
    int64 updateTimeMs = 26;
    bool isRemind = 27;
    bool isDissolved = 30;
    bool isMeeting = 31;
    string lastVisibleMessageId = 32;
    string lastThreadId = 33;
    int32 newThreadCount = 34;
    int32 lastThreadPosition = 35;
    bool isCrypto = 36;
    int32 noBadgedNewThreadCount = 37;
    int32 threadStartPosition = 38;
    ChatMode chatMode = 39;
    bool isCrossTenant = 41;
    bool isTenant = 42;
    SupportView supportView = 43;
    int64 joinTimeMs = 44;
    int64 oncallId = 45;
    int32 lastVisibleMessagePosition = 46;
    int32 lastVisibleMessageNoBadgedCount = 47;
    int32 readPosition = 48;
    int32 readPositionBadgeCount = 49;
    int32 lastMessagePositionBadgeCount = 50;
    bool enableWatermark = 51;
    string sidebarId = 53;
    string namePy = 100;
    map<string, string> i18nNames = 101 [deprecated = true];
    I18nInf i18nInf = 102;
    int32 readThreadPosition = 103;
    int32 readThreadPositionBadgeCount = 104;
    int32 lastThreadPositionBadgeCount = 105;
    int32 lastVisibleThreadPosition = 106;
    string lastVisibleThreadId = 107;
    bool isPublicV2 = 109;
    bool allowPost = 111;
    int64 burnedTime = 112;
    int32 putChatterApplyCount = 113;
    bool showBanner = 114;
    bool isLargeGroup = 115;
    int32 firstChatMessagePosition = 116;
    repeated int32 tags = 117 [packed = false];
    map<string, string> extra = 118;
    bool isSamePageMeeting = 119;
    int64 myThreadsReadTimestamp = 120;
    int64 myThreadsLastTimestamp = 121;

    enum Type {
        UNKNOWN = 0;
        P2P = 1;
        GROUP = 2;
        TOPIC_GROUP = 3;
    }

    enum Status {
        NORMAL = 0;
        ARCHIVE = 1;
        DELETED = 2;
    }

    enum ChatMode {
        UNKNOWN_CHAT_MODE = 0;
        DEFAULT = 1;
        THREAD = 2;
        THREAD_V2 = 3;
    }

    enum SupportView {
        VIEW_UNKNOWN = 0;
        VIEW_P2PGROUP = 1;
        VIEW_MEETING = 2;
        VIEW_THREAD = 3;
        VIEW_CRYPTO = 4;
    }

    enum Role {
        IGNORE = 0;
        MEMBER = 1;
        VISITOR = 2;
        THREAD_FOLLOWER = 3;
    }

    message Announcement {
        string content = 1;
        int64 updateTime = 2;
        string lastEditorId = 3;
        string docUrl = 4;
        bool enableOpendoc = 5;
    }

    message I18nInf {
        map<string, string> i18nNames = 1;
    }
}


message FeedCard {
    string id = 1;
    Type type = 2;
    int64 updateTime = 3;
    bool isDelayed = 4;
    int64 parentCardId = 5;
    int64 rankTime = 6;
    FeedType feedType = 7;
    string imprId = 8;
    int64 updateTimeMs = 9;

    enum Type {
        UNKNOWN_TYPE = 0;
        CHAT = 1;
        MAIL = 2;
        DOC = 3;
        THREAD = 4;
        BOX = 5;
        OPENAPP = 6;
        TOPIC = 7;
        APP_CHAT = 8;
    }

    enum FeedType {
        TYPE_UNKNOWN = 0;
        TYPE_NEWS = 1;
        TYPE_SOCIAL = 2;
    }
}