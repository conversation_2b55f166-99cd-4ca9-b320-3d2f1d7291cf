import json

import requests

from builder.auth import FlyBookAuth
from builder.header import <PERSON>er<PERSON><PERSON><PERSON>
from builder.params import ParamsBuilder
from builder.proto import ProtoBuilder

class FlyBookApi:
    base_url = "https://internal-api-lark-api.feishu.cn/im/gateway/"
    get_csrf_token_url = "https://internal-api-lark-api.feishu.cn/accounts/csrf"
    get_user_info_url = "https://internal-api-lark-api.feishu.cn/accounts/web/user"
    def get_csrf_token(self, auth):
        headers = HeaderBuilder.build_get_csrf_token_header().get()
        params = ParamsBuilder.build_get_csrf_token_param().get()
        response = requests.post(self.get_csrf_token_url, headers=headers, cookies=auth.cookie, params=params, verify=False)
        res_json = response.json()
        x_csrf_token = response.cookies.get('swp_csrf_token')
        return res_json, x_csrf_token

    def get_user_info(self, auth, x_csrf_token):
        headers = HeaderBuilder.build_get_user_info_header(x_csrf_token).get()
        params = ParamsBuilder.build_get_user_info_param().get()
        response = requests.get(self.get_user_info_url, headers=headers, cookies=auth.cookie, params=params, verify=False)
        res_json = response.json()
        userId = res_json['data']['user']['id']
        return res_json, userId

    def search_some(self, auth, query):
        headers = HeaderBuilder.build_search_header().get()
        Packet = ProtoBuilder.build_search_request_proto(headers['x-request-id'], query)
        response = requests.post(self.base_url, headers=headers, cookies=auth.cookie, data=Packet.SerializeToString(), verify=False)
        SearchResponsePacket, userAndGroupIds = ProtoBuilder.decode_search_response_proto(response.content)
        return SearchResponsePacket, userAndGroupIds

    def create_chat(self, auth, userId):
        headers = HeaderBuilder.build_create_chat_header().get()
        Packet = ProtoBuilder.build_create_chat_request_proto(headers['x-request-id'], userId)
        response = requests.post(self.base_url, headers=headers, cookies=auth.cookie, data=Packet.SerializeToString(), verify=False)
        PutChatResponsePacket, chatId = ProtoBuilder.decode_create_chat_response_proto(response.content)
        return PutChatResponsePacket, chatId

    def send_msg(self, auth, sends_text, chatId):
        headers = HeaderBuilder.build_send_msg_header().get()
        Packet = ProtoBuilder.build_send_message_request_proto(sends_text, headers['x-request-id'], chatId)
        response = requests.post(self.base_url, headers=headers, cookies=auth.cookie, data=Packet.SerializeToString(), verify=False)
        # 解码响应
        decoded_response = ProtoBuilder.decode_send_message_response_proto(response.content)
        return decoded_response



if __name__ == '__main__':
    fly_book_api = FlyBookApi()
    fly_book_auth = FlyBookAuth()
    cookie_str = 'passport_web_did=7507138464031457299; passport_trace_id=7507138464048021523; QXV0aHpDb250ZXh0=92a3a31bc0854c9cb734905143cc1fd3; _gcl_au=1.1.*********.**********; __tea__ug__uid=7507138400685032999; s_v_web_id=verify_mayxp6dz_64Armn4F_zkfb_4KDI_96tJ_JtwnHkZbe196; is_anonymous_session=; lang=zh; _csrf_token=95c77a808df924312c6583c08d7e035b0e76fe13-**********; help_center_session=********-2fa8-4c0f-91a3-8da84349bd05; _uuid_hera_ab_path_1=7507625345856798723; i18n_locale=zh-CN; lgw_csrf_token=19ce048f7d4385d24a120ad7453476bd13707bc2-**********; locale=zh-CN; _gid=GA1.2.**********.**********; login_recently=1; lob_csrf_token=e8c90a6e4f8858842e228531b5b01b93; landing_url=https://accounts.feishu.cn/accounts/page/login?app_id=7&force_login=1&no_trap=1&redirect_uri=https%3A%2F%2Fopen.feishu.cn%2Fapp; session=XN0YXJ0-fder417d-7e36-4113-ad61-30248fb2c32b-WVuZA; session_list=XN0YXJ0-6bcv25ec-1910-4aea-b4ce-9911867afb0a-WVuZA_XN0YXJ0-fder417d-7e36-4113-ad61-30248fb2c32b-WVuZA; passport_app_access_token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************.6aGrEmaMBA6PaajRjI4MIVU5UtW-rXfa4oC-BCqa5JhGFoEXTpOB748OhUJT9hRwwdOrqhpFHLHwkRyRIHye2w; Hm_lvt_89b2f04167e2aa91e52105f43f897c29=**********,**********,**********; HMACCOUNT=B0199BD78497EA54; Hm_lpvt_89b2f04167e2aa91e52105f43f897c29=**********; Hm_lvt_a79616d9322d81f12a92402ac6ae32ea=**********,**********,**********,**********; Hm_lpvt_a79616d9322d81f12a92402ac6ae32ea=**********; _uetsid=ed09c5a03eb411f09f78cbbcc18adc3c; _uetvid=f0295e4037d511f0bee297306b458456; _ga_VPYRHN104D=GS2.1.s1748760724$o12$g1$t1748760724$j60$l0$h0; _ga=GA1.2.**********.**********; x_lsc_cac_token=eyJhbGciOiJFUzI1NiIsImZlYXR1cmVfY29kZSI6ImxzbV9jb25kX2FjY2VzcyIsImtpZCI6Ijc1MDk2NTExNjY4NTU2MTAzNzAiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************.o3K3DZRcgT-mnAK6TtA6j50cdTRPG_Gjc8c4Cu1U6ahvTj88Ko8yd9gsPri3HmOHrI472tcAMH3aVkaUhpKPwQ; sl_session=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.t9uRMsCLE11tPqC65O-SBbr6_f8tdEObuGcWHNTOUw8lyx3__KcPshsX1GARYxVJ1nasLLOpCY4CIzTtUoKCDA; swp_csrf_token=130886a4-7145-44d9-a417-2e934a56a694; t_beda37=cd94243c9651d410b7e3558d13c93a61f0d8c933725f478e936800a17951ef47; msToken=luuQJIJzt-dp7QZ0MAlkLOaLFlPxeZFsAzVjWWu-i2r2koZeNTkJHaoQW_jeOxChHnhZgh7n8N5xkXr0x2ShBv_qw3EfjH57Z-C-v-TIFTBSRRw-0ocSBhfRDNCgtj0RmmPOkFb4hnhSvuFezXNN7hp7YHLnO8YBfjQKaA8Tko8FrOzMsOkWew=='
    fly_book_auth.perepare_auth(cookie_str)

    res_json, x_csrf_token = fly_book_api.get_csrf_token(fly_book_auth)
    res_json, userId = fly_book_api.get_user_info(fly_book_auth, x_csrf_token)
    # ========================================================================================================
    query = 'ShellBot'
    SearchResponsePacket, userAndGroupIds = fly_book_api.search_some(fly_book_auth, query)
    user_or_group_id = userAndGroupIds[0]
    if user_or_group_id['type'] == 'user':
        print('搜索到用户')
        userId = user_or_group_id['id']
        PutChatResponsePacket, chatId = fly_book_api.create_chat(fly_book_auth, userId)
        # print(chatId)
    else:
        print('搜索到群组')
        chatId = user_or_group_id['id']

    # ========================================================================================================
    # userId = "7355149382943080450"
    # PutChatResponsePacket, chatId = fly_book_api.create_chat(fly_book_auth, userId)
    # print(chatId)

    # sends_text = '测试消息'
    # # chatId = "7379910344918188035"
    # res = fly_book_api.send_msg(fly_book_auth, sends_text, chatId)
    # print(res)
